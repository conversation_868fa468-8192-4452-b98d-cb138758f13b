"use client";

import React, { useState, useEffect, useRef } from 'react';
import { LucideShieldCheck, LucideDollarSign, LucideZap, LucideWalletCards, LucideTrendingUp as LucideTrendingUp2, LucideLayoutGrid, LucideRefreshCcw, LucideHandCoins, LucideBarChart4 } from 'lucide-react';
import { useInView } from 'react-intersection-observer';

// Inline SVG Icons to replace react-icons and Lucide for a self-contained file
const IconLineChart = (props) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M3 3v18h18" />
    <path d="M18.7 8l-5.1 5.2L7 7" />
    <path d="M18.7 8L22 4" />
    <path d="M7 7l-3.2 3.2" />
  </svg>
);

const IconTrendingUp = (props) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" />
    <polyline points="16 7 22 7 22 13" />
  </svg>
);

const IconWallet = (props) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M19 7V4a1 1 0 0 0-1-1H3a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4" />
    <path d="M20 9h-7a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2V11a2 2 0 0 0-2-2z" />
    <circle cx="16" cy="15" r="1" />
  </svg>
);

const IconRocket = (props) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2L14 9l-3-3L4.5 16.5z" />
    <path d="m10 10 3-3L22 4l-3 3-3-3-3 3z" />
    <path d="m18 13-1.5-1.5" />
    <path d="m21 16-3-3" />
    <path d="m22 21-1-1" />
  </svg>
);

const IconArrowRightLeft = (props) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M8 3L4 7l4 4" />
    <path d="M4 7h16" />
    <path d="M16 21l4-4-4-4" />
    <path d="M20 17H4" />
  </svg>
);

const IconDatabase = (props) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <ellipse cx="12" cy="5" rx="9" ry="3" />
    <path d="M3 5V19A9 3 0 0 0 21 19V5" />
    <path d="M3 12A9 9 0 0 0 21 12" />
  </svg>
);

const IconUserPlus = (props) => (
  <svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
    <circle cx="8.5" cy="7" r="4" />
    <line x1="20" y1="8" x2="20" y2="14" />
    <line x1="23" y1="11" x2="17" y2="11" />
  </svg>
);

// Lucide icons
const LucideShieldCheck = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path><path d="m9 12 2 2 4-4"></path></svg>);
const LucideDollarSign = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="12" y1="1" x2="12" y2="23"></line><path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path></svg>);
const LucideZap = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg>);
const LucideWalletCards = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="2" width="18" height="18" rx="2"></rect><path d="M3 6.666V4a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v2.666"></path><path d="M10 12.666h.01"></path><path d="M14 12.666h.01"></path><path d="M18 12.666h.01"></path><path d="M3 16.666V14a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v2.666"></path><path d="M10 20.666h.01"></path><path d="M14 20.666h.01"></path><path d="M18 20.666h.01"></path></svg>);
const LucideTrendingUp2 = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg>);
const LucideLayoutGrid = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="3" y="3" width="7" height="7" rx="1"></rect><rect x="14" y="3" width="7" height="7" rx="1"></rect><rect x="14" y="14" width="7" height="7" rx="1"></rect><rect x="3" y="14" width="7" height="7" rx="1"></rect></svg>);
const LucideRefreshCcw = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M20 18A6 6 0 1 1 10 10L4 10"></path><path d="M4 14L10 10L4 4"></path></svg>);
const LucideHandCoins = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M11 15H11.5L13.5 17H19C20.1 17 21 16.1 21 15C21 13.9 20.1 13 19 13H15.5L13.5 11H18C19.1 11 20 10.1 20 9C20 7.9 19.1 7 18 7H14"></path><path d="M2 20V12H4L7 9.5L10 12L12 10.5L16 14.5V20H2Z"></path></svg>);
const LucideBarChart4 = (props) => (<svg {...props} xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 20V10"></path><path d="M12 20V4"></path><path d="M6 20V14"></path></svg>);



const sponsors = [
    { name: 'Bloomberg', src: 'https://placehold.co/180x60/1C1E26/9CA3AF?text=BLOOMBERG' },
    { name: 'Forbes', src: 'https://placehold.co/180x60/1C1E26/9CA3AF?text=FORBES' },
    { name: 'Yahoo Finance', src: 'https://placehold.co/180x60/1C1E26/9CA3AF?text=YAHOO!+FINANCE' },
    { name: 'CoinDesk', src: 'https://placehold.co/180x60/1C1E26/9CA3AF?text=COINDESK' },
    { name: 'Business Insider', src: 'https://placehold.co/180x60/1C1E26/9CA3AF?text=BUSINESS+INSIDER' },
];

const faqs = [
    {
        q: 'Is Identity Verification required?',
        a: 'Yes, Identity Verification is a crucial step to ensure the security of your account and comply with global regulatory standards, safeguarding your assets and providing a secure trading environment for all users.'
    },
    {
        q: 'How can I enhance the security of my account?',
        a: 'We strongly recommend enabling Two-Factor Authentication (2FA) and using a strong, unique password. Additionally, be cautious of phishing attempts and never share your login credentials with anyone.'
    },
    {
        q: 'How can I make a deposit?',
        a: 'Nimbit offers a seamless deposit process with various methods, including cryptocurrency transfers, fiat bank transfers, and credit/debit card payments, ensuring you can fund your account instantly and effortlessly.'
    },
    {
        q: 'What are the trading fees on Nimbit?',
        a: 'Nimbit offers one of the most competitive fee structures in the industry, with tiered discounts for high-volume traders and additional benefits for users who hold our native nimbit token.'
    }
];

const FaqItem = ({ question, answer }) => {
    const [isOpen, setIsOpen] = useState(false);
    return (
        <div className="border-b border-[#2A2E39] py-5 transition-all duration-300 ease-in-out">
            <button
                className="flex justify-between items-center w-full text-left text-white text-lg font-medium focus:outline-none"
                onClick={() => setIsOpen(!isOpen)}
            >
                <span>{question}</span>
                <svg
                    className={`w-6 h-6 transform transition-transform duration-300 ${isOpen ? 'rotate-180' : 'rotate-0'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                    />
                </svg>
            </button>
            {isOpen && (
                <div className="mt-4 text-[#9CA3AF] text-base leading-relaxed animate-fade-in-down">
                    {answer}
                </div>
            )}
        </div>
    );
};

// SVG for a placeholder chart
const UpChart = () => (
    <svg width="60" height="30" viewBox="0 0 60 30" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 28.5L10 18.5L20 23.5L30 8.5L40 13.5L50 3.5L59 1" stroke="#16C784" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
);

const DownChart = () => (
    <svg width="60" height="30" viewBox="0 0 60 30" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 1L10 11L20 6L30 21L40 16L50 26L59 28.5" stroke="#EA3943" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
);

const AnimatedNumber = ({ target, label }) => {
    const [count, setCount] = useState(0);
    const { ref, inView } = useInView({ threshold: 0.5, triggerOnce: true });

    const parseTarget = (t) => {
        let numericBase = 0;
        let prefix = '';
        let suffix = '';
        if (typeof t === 'string') {
            const numMatch = t.match(/(\d+\.?\d*)/);
            const nonNumMatch = t.match(/(\D+)/g);
            if (numMatch) {
                numericBase = parseFloat(numMatch[0]);
                if (nonNumMatch) {
                    prefix = nonNumMatch[0].includes('$') ? '$' : '';
                    suffix = nonNumMatch[nonNumMatch.length - 1];
                }
            }
        } else {
            numericBase = t;
        }
        return { numericBase, prefix, suffix };
    };

    const { numericBase, prefix, suffix } = parseTarget(target);

    useEffect(() => {
        if (inView) {
            let start = null;
            const duration = 1000;

            const animate = (timestamp) => {
                if (!start) start = timestamp;
                const progress = timestamp - start;
                const value = Math.min(progress / duration, 1) * numericBase;
                setCount(value);
                if (progress < duration) {
                    requestAnimationFrame(animate);
                }
            };
            requestAnimationFrame(animate);
        }
    }, [inView, numericBase]);

    let displayedValue = count;
    if (suffix.includes('K')) {
        displayedValue = parseFloat(count.toFixed(1));
    } else {
        displayedValue = Math.floor(count);
    }
    
    const displayValue = `${prefix}${displayedValue.toLocaleString()}${suffix}`;

    return (
        <div ref={ref} className="flex flex-col items-center">
            <span className="text-5xl md:text-6xl text-[#FFD600] font-bold">
                {inView ? displayValue : `${prefix}0${suffix}`}
            </span>
            <span className="text-sm text-[#9CA3AF]">{label}</span>
        </div>
    );
};


// New Card component with gradient border effect
const BorderGlowCard = ({ title, subtitle, Icon }) => {
  return (
    <div className="relative group overflow-hidden rounded-2xl p-px transition-all duration-300 hover:scale-[1.03] before:absolute before:inset-0 before:z-0 before:bg-[radial-gradient(400px_at_50%_50%,rgba(255,214,0,0.8)_0%,transparent_70%)] before:opacity-0 before:transition-opacity before:duration-500 hover:before:opacity-100 before:pointer-events-none">
      <div className="relative z-10 w-full p-8 rounded-[1.2rem] bg-[#1C1E26] border border-[#2A2E39] shadow-md transition-colors duration-300">
        <div className="flex items-start mb-4">
          <div className="bg-[#1C1E26] p-4 rounded-full border border-[#2A2E39] text-white group-hover:bg-[#FFD600] group-hover:text-[#1C1E26] transition-colors duration-300">
            <Icon size={32} />
          </div>
        </div>
        <h3 className="font-semibold text-lg sm:text-xl text-white mb-2 transition-colors duration-300">
          {title}
        </h3>
        <p className="text-sm text-[#9CA3AF] transition-colors duration-300">
          {subtitle}
        </p>
      </div>
    </div>
  );
};


const HomePage = () => {
    return (
        <div className="bg-[#0D0E12] text-white font-['Poppins'] antialiased overflow-x-hidden min-h-screen">
            <style>{`
                @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
                
                @keyframes marquee {
                    0% { transform: translateX(0); }
                    100% { transform: translateX(-50%); }
                }
                .animate-marquee {
                    animation: marquee 30s linear infinite;
                }
                @keyframes fadeInDown {
                    from { opacity: 0; transform: translateY(-10px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                .animate-fade-in-down {
                    animation: fadeInDown 0.5s ease-out;
                }
                @keyframes fadeInUp {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }
                .animate-fade-in-up {
                    animation: fadeInUp 0.7s ease-out;
                }
                @keyframes slideUpInitial {
                    from { transform: translateY(100%); }
                    to { transform: translateY(0); }
                }
                .animate-slide-up-initial {
                    animation: slideUpInitial 0.5s ease-out forwards;
                }
            `}</style>
            
            {/* Top Navigation Bar */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-[#0D0E12] bg-opacity-80 backdrop-blur-xl py-4 px-8 md:px-16 flex justify-between items-center border-b border-[#2A2E39]">
                <div className="flex items-center gap-12">
                    <div className="font-bold text-3xl text-[#FFD600] tracking-tight">nimbit</div>
                    <nav className="hidden lg:flex gap-10 text-[#9CA3AF] font-medium">
                        <a href="#" className="hover:text-white transition-colors duration-200">Markets</a>
                        <a href="#" className="hover:text-white transition-colors duration-200">Trade</a>
                        <a href="#" className="hover:text-white transition-colors duration-200">Earn</a>
                        <a href="#" className="hover:text-white transition-colors duration-200">More</a>
                    </nav>
                </div>
                <div className="flex items-center gap-4">
                    <button className="text-[#9CA3AF] hover:text-white transition-colors duration-200 font-medium hidden md:block">Log In</button>
                    <button className="bg-[#FFD600] text-[#1C1E26] font-bold py-2 px-5 rounded-xl hover:bg-[#FF9800] transition-colors duration-200 text-sm">
                        Sign Up
                    </button>
                </div>
            </header>

            {/* Main Content Sections */}
            <main>
                {/* Hero Section */}
                <section className="relative flex flex-col items-center justify-center text-center pt-48 pb-32 px-4 md:px-8 overflow-hidden">
                    <div className="absolute inset-0 z-0 bg-cover bg-center opacity-20" style={{ backgroundImage: 'url("https://images.unsplash.com/photo-1518458028782-9602280d940c?q=80&w=1740&auto=format&fit=crop")' }}></div>
                    <div className="relative z-10 max-w-5xl mx-auto">
                        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight tracking-tighter">
                            The Gateway to Your <span className="text-[#FFD600]">Crypto Future</span>
                        </h1>
                        <p className="text-[#9CA3AF] text-lg md:text-xl max-w-3xl mx-auto mb-10 leading-relaxed">
                            Discover a seamless and secure trading experience with industry-leading liquidity and a professional-grade platform trusted by millions worldwide.
                        </p>
                        <button className="bg-[#FFD600] text-[#1C1E26] font-bold py-3 px-8 rounded-xl text-md hover:bg-[#FF9800] transition-transform duration-200 transform hover:scale-105 shadow-2xl">
                            Start Trading Now
                            <span className="ml-3 text-2xl font-light">&rarr;</span>
                        </button>
                    </div>
                </section>

                {/* Sponsor Banner Section */}
                <section className="py-8 bg-[#1C1E26] border-y border-[#2A2E39] overflow-hidden">
                    <div className="flex items-center space-x-12 animate-marquee">
                        {[...sponsors, ...sponsors].map((sponsor, index) => (
                            <img key={index} src={sponsor.src} alt={sponsor.name} className="h-10 opacity-70 hover:opacity-100 transition-opacity duration-300" />
                        ))}
                    </div>
                </section>

                {/* Market Data Section */}
                <section className="py-20 px-4 md:px-16 max-w-screen-xl mx-auto">
                    <div className="flex justify-between items-center mb-10">
                        <h2 className="text-3xl font-bold">Market Overview</h2>
                        <a href="#" className="text-[#FFD600] text-sm font-medium hover:underline">
                            View All Markets &rarr;
                        </a>
                    </div>
                    <div className="bg-[#1C1E26] rounded-2xl p-6 shadow-xl">
                        <table className="min-w-full text-left text-sm">
                            <thead>
                                <tr className="text-[#9CA3AF] font-medium">
                                    <th className="py-3 px-4 rounded-tl-lg">Pair</th>
                                    <th className="py-3 px-4">Last Price</th>
                                    <th className="py-3 px-4">24h Change</th>
                                    <th className="py-3 px-4 hidden md:table-cell">Chart</th>
                                    <th className="py-3 px-4 hidden md:table-cell">24h High</th>
                                    <th className="py-3 px-4 hidden md:table-cell">24h Low</th>
                                    <th className="py-3 px-4 hidden md:table-cell">24h Vol (USDT)</th>
                                    <th className="py-3 px-4 rounded-tr-lg">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr className="text-white hover:bg-[#2A2E39] transition-colors duration-200">
                                    <td className="py-4 px-4 flex items-center gap-3 font-semibold">
                                        <img src="https://assets.coingecko.com/coins/images/1/large/bitcoin.png?1696501400" alt="BTC" className="w-8 h-8 rounded-full" />
                                        BTC/USDT
                                    </td>
                                    <td className="py-4 px-4">$109,040.80</td>
                                    <td className="py-4 px-4 text-[#16C784] font-semibold">+0.27%</td>
                                    <td className="py-4 px-4 hidden md:table-cell"><UpChart /></td>
                                    <td className="py-4 px-4 hidden md:table-cell">$109,500.00</td>
                                    <td className="py-4 px-4 hidden md:table-cell">$108,800.00</td>
                                    <td className="py-4 px-4 hidden md:table-cell">12,500,000</td>
                                    <td className="py-4 px-4">
                                        <button className="bg-[#FFD600] text-[#1C1E26] font-semibold text-xs px-4 py-2 rounded-full hover:bg-[#FF9800]">Trade</button>
                                    </td>
                                </tr>
                                <tr className="text-white hover:bg-[#2A2E39] transition-colors duration-200">
                                    <td className="py-4 px-4 flex items-center gap-3 font-semibold">
                                        <img src="https://assets.coingecko.com/coins/images/279/large/ethereum.png?1696501628" alt="ETH" className="w-8 h-8 rounded-full" />
                                        ETH/USDT
                                    </td>
                                    <td className="py-4 px-4">$4,457.76</td>
                                    <td className="py-4 px-4 text-[#EA3943] font-semibold">-1.12%</td>
                                    <td className="py-4 px-4 hidden md:table-cell"><DownChart /></td>
                                    <td className="py-4 px-4 hidden md:table-cell">$4,500.00</td>
                                    <td className="py-4 px-4 hidden md:table-cell">$4,400.00</td>
                                    <td className="py-4 px-4 hidden md:table-cell">8,900,000</td>
                                    <td className="py-4 px-4">
                                        <button className="bg-[#FFD600] text-[#1C1E26] font-semibold text-xs px-4 py-2 rounded-full hover:bg-[#FF9800]">Trade</button>
                                    </td>
                                </tr>
                                <tr className="text-white hover:bg-[#2A2E39] transition-colors duration-200">
                                    <td className="py-4 px-4 flex items-center gap-3 font-semibold">
                                        <img src="https://assets.coingecko.com/coins/images/44/large/xrp.png?1696501438" alt="XRP" className="w-8 h-8 rounded-full" />
                                        XRP/USDT
                                    </td>
                                    <td className="py-4 px-4">$0.589</td>
                                    <td className="py-4 px-4 text-[#16C784] font-semibold">*****%</td>
                                    <td className="py-4 px-4 hidden md:table-cell"><UpChart /></td>
                                    <td className="py-4 px-4 hidden md:table-cell">$0.600</td>
                                    <td className="py-4 px-4 hidden md:table-cell">$0.580</td>
                                    <td className="py-4 px-4 hidden md:table-cell">5,200,000</td>
                                    <td className="py-4 px-4">
                                        <button className="bg-[#FFD600] text-[#1C1E26] font-semibold text-xs px-4 py-2 rounded-full hover:bg-[#FF9800]">Trade</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                {/* Why Choose Nimbit Section */}
                <section className="py-20 px-4 md:px-16 max-w-7xl mx-auto text-center">
                    <h2 className="text-3xl font-bold mb-4">Why Choose Nimbit?</h2>
                    <p className="text-[#9CA3AF] text-lg mb-16 max-w-3xl mx-auto">Experience the difference with a platform designed for both professionals and beginners, offering unmatched security, liquidity, and product innovation.</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
                        <div className="bg-[#1C1E26] p-8 rounded-2xl shadow-xl flex flex-col items-center border-t-4 border-x border-b border-[#FFD600]">
                            <div className="bg-[#FFD600] text-[#1C1E26] p-4 rounded-full mb-6">
                                <LucideShieldCheck size={40} />
                            </div>
                            <h3 className="text-2xl font-bold mb-3">Unmatched Security</h3>
                            <p className="text-[#9CA3AF]">Our multi-layered security protocols and robust infrastructure ensure your assets are always protected against any threat.</p>
                        </div>
                        <div className="bg-[#1C1E26] p-8 rounded-2xl shadow-xl flex flex-col items-center border-t-4 border-x border-b border-[#FFD600]">
                            <div className="bg-[#FFD600] text-[#1C1E26] p-4 rounded-full mb-6">
                                <LucideDollarSign size={40} />
                            </div>
                            <h3 className="text-2xl font-bold mb-3">Deep Liquidity</h3>
                            <p className="text-[#9CA3AF]">With one of the largest order books and a wide range of markets, you can execute trades of any size with minimal slippage.</p>
                        </div>
                        <div className="bg-[#1C1E26] p-8 rounded-2xl shadow-xl flex flex-col items-center border-t-4 border-x border-b border-[#FFD600]">
                            <div className="bg-[#FFD600] text-[#1C1E26] p-4 rounded-full mb-6">
                                <LucideZap size={40} />
                            </div>
                            <h3 className="text-2xl font-bold mb-3">Innovative Products</h3>
                            <p className="text-[#9CA3AF]">Access a suite of advanced products, including futures, options, and structured products, to diversify and optimize your portfolio.</p>
                        </div>
                    </div>
                </section>

                {/* Discover Our Products Section with new cards */}
                <section className="py-20 px-4 md:px-16 max-w-7xl mx-auto">
                    <div className="text-center mb-16">
                        <h2 className="text-3xl font-bold mb-4">Discover Our Products</h2>
                        <p className="text-[#9CA3AF] text-lg max-w-3xl mx-auto">
                            Explore our diverse suite of innovative financial instruments designed to help you trade, earn, and grow your digital assets.
                        </p>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        {/* Spot Trading Card */}
                        <BorderGlowCard
                          title="Spot Trading"
                          subtitle="Trade a wide range of cryptocurrencies with real-time market data and low fees."
                          Icon={IconLineChart}
                        />

                        {/* Futures Trading Card */}
                        <BorderGlowCard
                          title="Futures"
                          subtitle="Leverage your positions and manage risk with our advanced futures platform."
                          Icon={IconTrendingUp}
                        />

                        {/* Nimbit Earn Card */}
                        <BorderGlowCard
                          title="Nimbit Earn"
                          subtitle="Grow your crypto assets with high-yield savings and staking products."
                          Icon={IconWallet}
                        />

                        {/* Launchpad Card */}
                        <BorderGlowCard
                          title="Launchpad"
                          subtitle="Access exclusive token sales from the most promising projects."
                          Icon={IconRocket}
                        />

                        {/* Margin Trading Card */}
                        <BorderGlowCard
                          title="Margin Trading"
                          subtitle="Amplify your trading power with leverage and diverse assets."
                          Icon={IconArrowRightLeft}
                        />

                        {/* Options Card */}
                        <BorderGlowCard
                          title="Options"
                          subtitle="Hedge your portfolio or speculate on future price movements."
                          Icon={IconDatabase}
                        />
                    </div>
                </section>

                {/* Nimbit By The Numbers Section */}
                <section className="py-20 px-4 md:px-16 max-w-7xl mx-auto text-center bg-[#1C1E26] rounded-3xl shadow-xl">
                    <h2 className="text-3xl font-bold mb-4">Nimbit By The Numbers</h2>
                    <p className="text-[#9CA3AF] text-lg mb-10 max-w-3xl mx-auto">A look into our global reach and market-leading performance.</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                        <AnimatedNumber target="28M+" label="Users Worldwide" />
                        <AnimatedNumber target="7.2K+" label="Cryptocurrencies Listed" />
                        <AnimatedNumber target="$100B+" label="24h Trading Volume" />
                    </div>
                    <button className="bg-[#FFD600] text-[#1C1E26] font-bold py-3 px-8 rounded-xl text-md hover:bg-[#FF9800] transition-transform duration-200 transform hover:scale-105 shadow-2xl">
                        Get Started
                        <span className="ml-3 text-xl font-light">&rarr;</span>
                    </button>
                </section>

                {/* Your 3-Step Journey to Crypto Section */}
                <section className="py-20 px-4 md:px-16 max-w-7xl mx-auto text-center">
                    <h2 className="text-3xl font-bold mb-4">Your 3-Step Journey to Crypto</h2>
                    <p className="text-[#9CA3AF] text-lg mb-16 max-w-3xl mx-auto">Get started with Nimbit in just a few simple steps and take control of your financial future.</p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="flex flex-col items-center text-center bg-[#1C1E26] p-8 rounded-2xl shadow-xl border-t-4 border-x border-b border-[#FFD600]">
                            <div className="flex items-center justify-center w-20 h-20 mb-6 bg-[#1C1E26] rounded-full border-2 border-[#2A2E39]">
                                <IconUserPlus size={40} className="text-[#FFD600]" />
                            </div>
                            <div className="text-white text-3xl font-bold mb-2">Step 1</div>
                            <h3 className="text-xl font-semibold mb-2">Create Your Account</h3>
                            <p className="text-[#9CA3AF] text-sm leading-relaxed">Sign up in seconds and complete our quick verification process to secure your profile.</p>
                        </div>
                        <div className="flex flex-col items-center text-center bg-[#1C1E26] p-8 rounded-2xl shadow-xl border-t-4 border-x border-b border-[#FFD600]">
                            <div className="flex items-center justify-center w-20 h-20 mb-6 bg-[#1C1E26] rounded-full border-2 border-[#2A2E39]">
                                <LucideWalletCards size={40} className="text-[#FFD600]" />
                            </div>
                            <div className="text-white text-3xl font-bold mb-2">Step 2</div>
                            <h3 className="text-xl font-semibold mb-2">Fund Your Wallet</h3>
                            <p className="text-[#9CA3AF] text-sm leading-relaxed">Deposit funds using a variety of methods, including crypto, bank transfers, or credit card.</p>
                        </div>
                        <div className="flex flex-col items-center text-center bg-[#1C1E26] p-8 rounded-2xl shadow-xl border-t-4 border-x border-b border-[#FFD600]">
                            <div className="flex items-center justify-center w-20 h-20 mb-6 bg-[#1C1E26] rounded-full border-2 border-[#2A2E39]">
                                <LucideTrendingUp2 size={40} className="text-[#FFD600]" />
                            </div>
                            <div className="text-white text-3xl font-bold mb-2">Step 3</div>
                            <h3 className="text-xl font-semibold mb-2">Start Trading</h3>
                            <p className="text-[#9CA3AF] text-sm leading-relaxed">Explore thousands of crypto pairs and make your first trade with confidence.</p>
                        </div>
                    </div>
                </section>

                {/* Trade Anytime, Anywhere Section */}
                <section className="py-20 px-4 md:px-16 max-w-screen-xl mx-auto flex flex-col items-center">
                    <div className="text-center mb-10">
                        <h2 className="text-4xl lg:text-5xl font-bold mb-4 leading-snug">Trade Anytime, Anywhere.</h2>
                        <p className="text-[#9CA3AF] text-lg max-w-3xl mx-auto leading-relaxed">Experience a truly seamless trading platform on your desktop or on the go with our dedicated mobile app.</p>
                    </div>

                    <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12">
                        <div className="flex items-center gap-4">
                            <div className="bg-white p-2 rounded-xl">
                                <img src="https://placehold.co/100x100/FFFFFF/000000?text=QR+Code" alt="QR Code" className="w-24 h-24" />
                            </div>
                            <div className="flex flex-col gap-1">
                                <span className="text-xs text-[#9CA3AF] font-medium tracking-wide">Scan to Download</span>
                                <span className="font-semibold text-white text-lg">for iOS & Android</span>
                            </div>
                        </div>
                        <div className="h-10 w-px bg-[#2A2E39] hidden sm:block"></div>
                        <div className="flex gap-4">
                            <a href="#" aria-label="Apple App Store">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3c/Download_on_the_App_Store_Badge.svg/800px-Download_on_the_App_Store_Badge.svg.png" alt="App Store" className="h-12 rounded-lg hover:scale-105 transition-transform duration-200" />
                            </a>
                            <a href="#" aria-label="Google Play Store">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/7/78/Google_Play_Store_badge_EN.svg/800px-Google_Play_Store_badge_EN.svg.png" alt="Google Play" className="h-12 rounded-lg hover:scale-105 transition-transform duration-200" />
                            </a>
                        </div>
                    </div>

                    <div className="relative w-full max-w-5xl mx-auto mt-12">
                        <img src="https://placehold.co/1000x562/1C1E26/9CA3AF?text=Desktop+Trading+UI" alt="Live Trading UI" className="w-full rounded-3xl shadow-2xl border-2 border-[#2A2E39] z-10 relative" />
                        <img src="https://placehold.co/300x600/1C1E26/9CA3AF?text=Mobile+App+UI" alt="Mobile App UI" className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform w-1/4 rounded-3xl shadow-2xl border-2 border-[#2A2E39] z-20 transition-all duration-300 hover:scale-105" />
                    </div>
                </section>


                {/* FAQs Section */}
                <section className="py-20 px-4 md:px-16 max-w-4xl mx-auto">
                    <h2 className="text-3xl font-bold mb-10 text-center">Frequently Asked Questions</h2>
                    <div className="space-y-4">
                        {faqs.map((faq, index) => (
                            <FaqItem key={index} question={faq.q} answer={faq.a} />
                        ))}
                    </div>
                </section>

                {/* Final CTA Banner */}
                <section className="py-24 px-4 md:px-16 bg-[#1C1E26] text-center">
                    <h2 className="text-4xl font-bold mb-4 leading-snug">Embark on Your Crypto Journey Today!</h2>
                    <p className="text-[#9CA3AF] text-lg mb-10 max-w-3xl mx-auto">Join millions of users who trust Nimbit for their crypto trading and take advantage of our exclusive new user bonus.</p>
                    <button className="bg-[#FFD600] text-[#1C1E26] font-bold py-3 px-8 rounded-xl text-md hover:bg-[#FF9800] transition-transform duration-200 transform hover:scale-105 shadow-2xl">
                        Sign Up Now
                        <span className="ml-3 text-xl font-light">&rarr;</span>
                    </button>
                </section>
            </main>

            {/* Sticky Bottom Banner for Bonus */}
            <div className="fixed bottom-0 left-0 right-0 bg-[#FFD600] text-[#1C1E26] p-4 text-center text-sm font-semibold z-50 transform translate-y-full animate-slide-up-initial">
                <p>Quickly register to join the bull market, complete tasks, and claim up to $5,050 USDT gift today! <a href="#" className="underline ml-2">Claim</a></p>
            </div>

            {/* Footer Section */}
            <footer className="bg-[#0D0E12] py-20 px-4 md:px-16 text-[#9CA3AF]">
                <div className="max-w-screen-xl mx-auto grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-12">
                    <div className="col-span-2 md:col-span-1 lg:col-span-2">
                        <div className="font-bold text-3xl text-white mb-4 tracking-tight">nimbit</div>
                        <p className="text-sm leading-relaxed max-w-xs">A global digital asset exchange with high liquidity and unmatched security, trusted by millions of users worldwide.</p>
                        <div className="flex gap-4 mt-6">
                            <a href="#" aria-label="Twitter">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg" className="text-[#9CA3AF] hover:text-white transition-colors duration-200">
                                    <path d="M18.244 2.25h3.308l-7.227 8.261 8.503 11.239H16.245l-5.263-6.188L4.755 21.75H1.44l7.63-10.914L1 2.25h3.308l5.49 7.375L18.244 2.25zm-2.91 17.513h1.347L6.852 4.09h-1.342l10.325 15.673z"></path>
                                </svg>
                            </a>
                            <a href="#" aria-label="Discord">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg" className="text-[#9CA3AF] hover:text-white transition-colors duration-200">
                                    <path d="M21.576 2.454a.89.89 0 0 0-.749-.785C19.805 1.258 17.067 1 13.99 1c-3.078 0-5.816.258-6.837.671a.89.89 0 0 0-.75.785.89.89 0 0 0 .193.585c1.455 1.583 3.09 3.256 5.094 4.88l.1.082.028-.02c2.003-1.624 3.639-3.297 5.093-4.88a.89.89 0 0 0 .194-.585zM7.221 16.486c-.957 0-1.74-.805-1.74-1.79s.783-1.79 1.74-1.79c.957 0 1.74.805 1.74 1.79s-.783 1.79-1.74 1.79zm9.558 0c-.957 0-1.74-.805-1.74-1.79s.783-1.79 1.74-1.79c.957 0 1.74.805 1.74 1.79s-.783 1.79-1.74 1.79zM20.217 9.87c-1.12-1.077-2.316-2.115-3.525-3.085-1.18-.94-2.327-1.79-3.414-2.522-1.087-.73-2.09-.995-2.924-1.018a.88.88 0 0 0-.276 0c-1.28.016-2.45.362-3.62.986-1.17.625-2.227 1.487-3.21 2.502-1.002 1.05-1.93 2.164-2.784 3.32-1.01 1.34-1.69 2.766-2.036 4.25-.34 1.485-.27 2.925-.133 4.316.136 1.39.53 2.72 1.14 3.972.585 1.21 1.285 2.29 2.146 3.264.86.975 1.8 1.815 2.805 2.52l.142.09.117-.184c.32-.497.66-.975 1.01-1.423.35-.45.698-.865 1.045-1.246.347-.38.68-.74 1.006-1.07.327-.33.64-.64 1.02-.916.377-.276.76-.51 1.15-.71.39-.197.8-.36 1.21-.49.41-.13.82-.23 1.23-.3a.89.89 0 0 0 .524.12c.548.06 1.096.183 1.638.374.542.19 1.06.448 1.55.76.49.31 1.02.668 1.58.985.56.315 1.15.58 1.74.795.59.215 1.19.355 1.8.42a.89.89 0 0 0 .493-.195c.5-.325 1.06-.82 1.63-1.46.57-.64 1.05-1.39 1.45-2.24.4-.85.73-1.8.97-2.82.23-1.01.37-2.07.4-3.14.03-1.07-.02-2.15-.15-3.21-.13-1.06-.39-2.08-.77-3.04zM16.5 12.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5zM7.5 12.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5z"></path>
                                </svg>
                            </a>
                            <a href="#" aria-label="Telegram">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg" className="text-[#9CA3AF] hover:text-white transition-colors duration-200">
                                    <path d="M21.576 2.454a.89.89 0 0 0-.749-.785C19.805 1.258 17.067 1 13.99 1c-3.078 0-5.816.258-6.837.671a.89.89 0 0 0-.75.785.89.89 0 0 0 .193.585c1.455 1.583 3.09 3.256 5.094 4.88l.1.082.028-.02c2.003-1.624 3.639-3.297 5.093-4.88a.89.89 0 0 0 .194-.585zM7.221 16.486c-.957 0-1.74-.805-1.74-1.79s.783-1.79 1.74-1.79c.957 0 1.74.805 1.74 1.79s-.783 1.79-1.74 1.79zm9.558 0c-.957 0-1.74-.805-1.74-1.79s.783-1.79 1.74-1.79c.957 0 1.74.805 1.74 1.79s-.783 1.79-1.74 1.79zM20.217 9.87c-1.12-1.077-2.316-2.115-3.525-3.085-1.18-.94-2.327-1.79-3.414-2.522-1.087-.73-2.09-.995-2.924-1.018a.88.88 0 0 0-.276 0c-1.28.016-2.45.362-3.62.986-1.17.625-2.227 1.487-3.21 2.502-1.002 1.05-1.93 2.164-2.784 3.32-1.01 1.34-1.69 2.766-2.036 4.25-.34 1.485-.27 2.925-.133 4.316.136 1.39.53 2.72 1.14 3.972.585 1.21 1.285 2.29 2.146 3.264.86.975 1.8 1.815 2.805 2.52l.142.09.117-.184c.32-.497.66-.975 1.01-1.423.35-.45.698-.865 1.045-1.246.347-.38.68-.74 1.006-1.07.327-.33.64-.64 1.02-.916.377-.276.76-.51 1.15-.71.39-.197.8-.36 1.21-.49.41-.13.82-.23 1.23-.3a.89.89 0 0 0 .524.12c.548.06 1.096.183 1.638.374.542.19 1.06.448 1.55.76.49.31 1.02.668 1.58.985.56.315 1.15.58 1.74.795.59.215 1.19.355 1.8.42a.89.89 0 0 0 .493-.195c.5-.325 1.06-.82 1.63-1.46.57-.64 1.05-1.39 1.45-2.24.4-.85.73-1.8.97-2.82.23-1.01.37-2.07.4-3.14.03-1.07-.02-2.15-.15-3.21-.13-1.06-.39-2.08-.77-3.04zM16.5 12.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5zM7.5 12.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5z"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div>
                        <h4 className="font-semibold text-white mb-6">About</h4>
                        <ul className="space-y-3 text-sm">
                            <li><a href="#" className="hover:underline">About Us</a></li>
                            <li><a href="#" className="hover:underline">Careers</a></li>
                            <li><a href="#" className="hover:underline">Blog</a></li>
                            <li><a href="#" className="hover:underline">Media</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-semibold text-white mb-6">Services</h4>
                        <ul className="space-y-3 text-sm">
                            <li><a href="#" className="hover:underline">One-Click Buy</a></li>
                            <li><a href="#" className="hover:underline">Spot Trading</a></li>
                            <li><a href="#" className="hover:underline">Futures</a></li>
                            <li><a href="#" className="hover:underline">VIP Program</a></li>
                        </ul>
                    </div>
                    <div>
                        <h4 className="font-semibold text-white mb-6">Support</h4>
                        <ul className="space-y-3 text-sm">
                            <li><a href="#" className="hover:underline">Support Hub</a></li>
                            <li><a href="#" className="hover:underline">User Feedback</a></li>
                            <li><a href="#" className="hover:underline">API Documentation</a></li>
                            <li><a href="#" className="hover:underline">Fees</a></li>
                        </ul>
                    </div>
                </div>
                <div className="mt-20 pt-8 border-t border-[#2A2E39] text-center text-xs">
                    <p>&copy; 2025 Nimbit. All Rights Reserved.</p>
                </div>
            </footer>
        </div>
    );
};

export default HomePage;
