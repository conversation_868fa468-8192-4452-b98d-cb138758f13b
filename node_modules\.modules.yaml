hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.3':
    '@babel/core': private
  '@babel/generator@7.28.3':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.28.3':
    '@babel/helpers': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.3)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.3)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.3)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.3)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.3)':
    '@babel/plugin-syntax-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.3':
    '@babel/traverse': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cbor-extract/cbor-extract-win32-x64@2.2.0':
    '@cbor-extract/cbor-extract-win32-x64': private
  '@colors/colors@1.6.0':
    '@colors/colors': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@esbuild/win32-x64@0.18.20':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.4':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.26.28(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@headlessui/react@2.2.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@headlessui/react': private
  '@hexagon/base64@1.1.28':
    '@hexagon/base64': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@next/env@14.2.32':
    '@next/env': private
  '@next/eslint-plugin-next@14.2.32':
    '@next/eslint-plugin-next': private
  '@next/swc-win32-x64-msvc@14.2.32':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@peculiar/asn1-android@2.4.0':
    '@peculiar/asn1-android': private
  '@peculiar/asn1-ecc@2.4.0':
    '@peculiar/asn1-ecc': private
  '@peculiar/asn1-rsa@2.4.0':
    '@peculiar/asn1-rsa': private
  '@peculiar/asn1-schema@2.4.0':
    '@peculiar/asn1-schema': private
  '@peculiar/asn1-x509@2.4.0':
    '@peculiar/asn1-x509': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@react-aria/focus@3.21.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@react-aria/focus': private
  '@react-aria/interactions@3.25.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@react-aria/interactions': private
  '@react-aria/ssr@3.9.10(react@18.3.1)':
    '@react-aria/ssr': private
  '@react-aria/utils@3.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@react-aria/utils': private
  '@react-stately/flags@3.1.2':
    '@react-stately/flags': private
  '@react-stately/utils@3.10.8(react@18.3.1)':
    '@react-stately/utils': private
  '@react-types/shared@3.32.0(react@18.3.1)':
    '@react-types/shared': private
  '@redis/bloom@1.2.0(@redis/client@1.6.1)':
    '@redis/bloom': private
  '@redis/client@1.6.1':
    '@redis/client': private
  '@redis/graph@1.1.1(@redis/client@1.6.1)':
    '@redis/graph': private
  '@redis/json@1.0.7(@redis/client@1.6.1)':
    '@redis/json': private
  '@redis/search@1.2.0(@redis/client@1.6.1)':
    '@redis/search': private
  '@redis/time-series@1.1.0(@redis/client@1.6.1)':
    '@redis/time-series': private
  '@reduxjs/toolkit@2.8.2(react-redux@9.2.0(@types/react@18.3.24)(react@18.3.1)(redux@5.0.1))(react@18.3.1)':
    '@reduxjs/toolkit': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': private
  '@simplewebauthn/server@8.3.7':
    '@simplewebauthn/server': private
  '@simplewebauthn/typescript-types@8.3.4':
    '@simplewebauthn/typescript-types': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.5':
    '@swc/helpers': private
  '@tailwindcss/node@4.1.12':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.12':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.12':
    '@tailwindcss/oxide': private
  '@tailwindcss/postcss@4.1.12':
    '@tailwindcss/postcss': private
  '@tanstack/react-virtual@3.13.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@tanstack/react-virtual': private
  '@tanstack/virtual-core@3.13.12':
    '@tanstack/virtual-core': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.28.0':
    '@types/babel__traverse': private
  '@types/bcryptjs@2.4.6':
    '@types/bcryptjs': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cors@2.8.19':
    '@types/cors': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@29.5.14':
    '@types/jest': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/jsonwebtoken@9.0.10':
    '@types/jsonwebtoken': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/passport-jwt@3.0.13':
    '@types/passport-jwt': private
  '@types/passport-local@1.0.38':
    '@types/passport-local': private
  '@types/passport-strategy@0.2.38':
    '@types/passport-strategy': private
  '@types/passport@1.0.17':
    '@types/passport': private
  '@types/pg@8.15.5':
    '@types/pg': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/qrcode@1.5.5':
    '@types/qrcode': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-dom@18.3.7(@types/react@18.3.24)':
    '@types/react-dom': private
  '@types/react@18.3.24':
    '@types/react': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/speakeasy@2.0.10':
    '@types/speakeasy': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.9.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-win32-x64-msvc@1.11.1':
    '@unrs/resolver-binding-win32-x64-msvc': private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asn1js@3.0.6:
    asn1js: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  autoprefixer@10.4.21(postcss@8.5.6):
    autoprefixer: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axios@1.11.0:
    axios: private
  axobject-query@4.1.0:
    axobject-query: private
  babel-jest@29.7.0(@babel/core@7.28.3):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.2.0(@babel/core@7.28.3):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.28.3):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  base32.js@0.0.1:
    base32.js: private
  bcryptjs@2.4.3:
    bcryptjs: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.4:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001739:
    caniuse-lite: private
  cbor-extract@2.2.0:
    cbor-extract: private
  cbor-x@1.6.0:
    cbor-x: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  chownr@3.0.0:
    chownr: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  client-only@0.0.1:
    client-only: private
  cliui@6.0.0:
    cliui: private
  clsx@2.1.1:
    clsx: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@3.2.1:
    color: private
  colorspace@1.1.4:
    colorspace: private
  combined-stream@1.0.8:
    combined-stream: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  cors@2.8.5:
    cors: private
  create-jest@29.7.0(@types/node@20.19.11):
    create-jest: private
  cross-fetch@4.1.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decamelize@5.0.1:
    decamelize: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  dedent@1.6.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  diff-sequences@29.6.3:
    diff-sequences: private
  dijkstrajs@1.0.3:
    dijkstrajs: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@2.1.0:
    doctrine: private
  doctrine@3.0.0:
    doctrine: private
  dotenv@16.6.1:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.211:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@9.2.2:
    emoji-regex: private
  enabled@2.0.0:
    enabled: private
  encodeurl@2.0.0:
    encodeurl: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es-toolkit@1.39.10:
    es-toolkit: private
  esbuild@0.18.20:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-config-next@14.2.32(eslint@8.57.1)(typescript@5.9.2):
    eslint-config-next: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1))(eslint@8.57.1):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.1(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1(eslint-plugin-import@2.32.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint@8.57.1))(eslint@8.57.1))(eslint@8.57.1):
    eslint-module-utils: private
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@6.21.0(eslint@8.57.1)(typescript@5.9.2))(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.0.0-canary-7118f5dd7-20230705(eslint@8.57.1):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter3@5.0.1:
    eventemitter3: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  express@4.21.2:
    express: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  fecha@4.2.3:
    fecha: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fn.name@1.1.0:
    fn.name: private
  follow-redirects@1.15.11:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.4:
    form-data: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  framer-motion@12.23.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    framer-motion: private
  fresh@0.5.2:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  generic-pool@3.9.0:
    generic-pool: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@10.3.10:
    glob: private
  glob@7.2.3:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  handlebars@4.7.8:
    handlebars: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  helmet@7.2.0:
    helmet: private
  html-escaper@2.0.2:
    html-escaper: private
  http-errors@2.0.0:
    http-errors: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  immer@10.1.1:
    immer: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@2.0.3:
    internmap: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@5.2.1:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.2.0:
    istanbul-reports: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@2.3.6:
    jackspeak: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@20.19.11):
    jest-cli: private
  jest-config@29.7.0(@types/node@20.19.11):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jest@29.7.0(@types/node@20.19.11):
    jest: private
  jiti@2.5.1:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  kuler@2.0.0:
    kuler: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  logform@2.7.0:
    logform: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@5.1.1:
    lru-cache: private
  lucide-react@0.542.0(react@18.3.1):
    lucide-react: private
  magic-string@0.30.18:
    magic-string: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@1.0.4:
    mkdirp: private
  mkdirp@3.0.1:
    mkdirp: private
  motion-dom@12.23.12:
    motion-dom: private
  motion-utils@12.23.6:
    motion-utils: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  napi-postinstall@0.3.3:
    napi-postinstall: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.3:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  next@14.2.32(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    next: private
  node-fetch@2.7.0:
    node-fetch: private
  node-gyp-build-optional-packages@5.1.1:
    node-gyp-build-optional-packages: private
  node-int64@0.4.0:
    node-int64: private
  node-pg-migrate@6.2.2(pg@8.16.3):
    node-pg-migrate: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  npm-run-path@4.0.1:
    npm-run-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  one-time@1.0.0:
    one-time: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  passport-jwt@4.0.1:
    passport-jwt: private
  passport-local@1.0.0:
    passport-local: private
  passport-strategy@1.0.0:
    passport-strategy: private
  passport@0.6.0:
    passport: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pause@0.0.1:
    pause: private
  pg-cloudflare@1.2.7:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.3):
    pg-pool: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pg@8.16.3:
    pg: private
  pgpass@1.0.5:
    pgpass: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pngjs@5.0.0:
    pngjs: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.4.31:
    postcss: private
  postcss@8.5.6:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@29.7.0:
    pretty-format: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  pvtsutils@1.3.6:
    pvtsutils: private
  pvutils@1.1.3:
    pvutils: private
  qrcode@1.5.4:
    qrcode: private
  qs@6.13.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-intersection-observer@9.16.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-intersection-observer: private
  react-is@18.3.1:
    react-is: private
  react-redux@9.2.0(@types/react@18.3.24)(react@18.3.1)(redux@5.0.1):
    react-redux: private
  react@18.3.1:
    react: private
  readable-stream@3.6.2:
    readable-stream: private
  recharts@3.1.2(@types/react@18.3.24)(react-dom@18.3.1(react@18.3.1))(react-is@18.3.1)(react@18.3.1)(redux@5.0.1):
    recharts: private
  redis@4.7.1:
    redis: private
  redux-thunk@3.1.0(redux@5.0.1):
    redux-thunk: private
  redux@5.0.1:
    redux: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  require-directory@2.1.1:
    require-directory: private
  require-main-filename@2.0.0:
    require-main-filename: private
  reselect@5.1.1:
    reselect: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.23.2:
    scheduler: private
  semver@6.3.1:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  speakeasy@2.0.0:
    speakeasy: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stable-hash@0.0.5:
    stable-hash: private
  stack-trace@0.0.10:
    stack-trace: private
  stack-utils@2.0.6:
    stack-utils: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  streamsearch@1.1.0:
    streamsearch: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.1(react@18.3.1):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tabbable@6.2.0:
    tabbable: private
  tailwindcss@4.1.12:
    tailwindcss: private
  tapable@2.2.3:
    tapable: private
  tar@7.4.3:
    tar: private
  test-exclude@6.0.0:
    test-exclude: private
  text-hex@1.0.0:
    text-hex: private
  text-table@0.2.0:
    text-table: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  triple-beam@1.4.1:
    triple-beam: private
  ts-api-utils@1.4.3(typescript@5.9.2):
    ts-api-utils: private
  ts-jest@29.4.1(@babel/core@7.28.3)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.28.3))(jest-util@29.7.0)(jest@29.7.0(@types/node@20.19.11))(typescript@5.9.2):
    ts-jest: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tsx@3.14.0:
    tsx: private
  turbo-windows-64@1.13.4:
    turbo-windows-64: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  type-fest@4.41.0:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uglify-js@3.19.3:
    uglify-js: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
  unrs-resolver@1.11.1:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.25.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  vary@1.1.2:
    vary: private
  victory-vendor@37.3.6:
    victory-vendor: private
  walker@1.0.8:
    walker: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-module@2.0.1:
    which-module: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  winston-transport@4.9.0:
    winston-transport: private
  winston@3.17.0:
    winston: private
  word-wrap@1.2.5:
    word-wrap: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  xtend@4.0.2:
    xtend: private
  y18n@4.0.3:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yallist@5.0.0:
    yallist: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.3.1:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod@3.25.76:
    zod: private
ignoredBuilds:
  - '@tailwindcss/oxide'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Sun, 31 Aug 2025 20:02:01 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@cbor-extract/cbor-extract-darwin-arm64@2.2.0'
  - '@cbor-extract/cbor-extract-darwin-x64@2.2.0'
  - '@cbor-extract/cbor-extract-linux-arm64@2.2.0'
  - '@cbor-extract/cbor-extract-linux-arm@2.2.0'
  - '@cbor-extract/cbor-extract-linux-x64@2.2.0'
  - '@emnapi/core@1.5.0'
  - '@emnapi/runtime@1.5.0'
  - '@emnapi/wasi-threads@1.1.0'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-ia32@0.18.20'
  - '@napi-rs/wasm-runtime@0.2.12'
  - '@next/swc-darwin-arm64@14.2.32'
  - '@next/swc-darwin-x64@14.2.32'
  - '@next/swc-linux-arm64-gnu@14.2.32'
  - '@next/swc-linux-arm64-musl@14.2.32'
  - '@next/swc-linux-x64-gnu@14.2.32'
  - '@next/swc-linux-x64-musl@14.2.32'
  - '@next/swc-win32-arm64-msvc@14.2.32'
  - '@next/swc-win32-ia32-msvc@14.2.32'
  - '@tailwindcss/oxide-android-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-arm64@4.1.12'
  - '@tailwindcss/oxide-darwin-x64@4.1.12'
  - '@tailwindcss/oxide-freebsd-x64@4.1.12'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.12'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.12'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.12'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.12'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.12'
  - '@tybys/wasm-util@0.10.0'
  - '@unrs/resolver-binding-android-arm-eabi@1.11.1'
  - '@unrs/resolver-binding-android-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-arm64@1.11.1'
  - '@unrs/resolver-binding-darwin-x64@1.11.1'
  - '@unrs/resolver-binding-freebsd-x64@1.11.1'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-arm64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-riscv64-musl@1.11.1'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-gnu@1.11.1'
  - '@unrs/resolver-binding-linux-x64-musl@1.11.1'
  - '@unrs/resolver-binding-wasm32-wasi@1.11.1'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.11.1'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.11.1'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - turbo-darwin-64@1.13.4
  - turbo-darwin-arm64@1.13.4
  - turbo-linux-64@1.13.4
  - turbo-linux-arm64@1.13.4
  - turbo-windows-arm64@1.13.4
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\crypto exchange\node_modules\.pnpm
virtualStoreDirMaxLength: 60
