{"lastValidatedTimestamp": 1756720704030, "projects": {"C:\\Users\\<USER>\\Documents\\crypto exchange": {"name": "nimbit-crypto-exchange", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\apps\\mobile": {"name": "@nimbit/mobile", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\apps\\web": {"name": "@nimbit/web", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\packages\\config": {"name": "@nimbit/config", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\packages\\types": {"name": "@nimbit/types", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\packages\\ui": {"name": "@nimbit/ui", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\packages\\utils": {"name": "@nimbit/utils", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\services\\auth-service": {"name": "@nimbit/auth-service", "version": "1.0.0"}, "C:\\Users\\<USER>\\Documents\\crypto exchange\\services\\database": {"name": "@nimbit/database", "version": "1.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*", "apps/*", "services/*"]}, "filteredInstall": true}