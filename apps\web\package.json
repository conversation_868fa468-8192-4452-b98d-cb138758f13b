{"name": "@nimbit/web", "version": "1.0.0", "description": "Nimbit crypto exchange web application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next"}, "dependencies": {"@headlessui/react": "^2.2.7", "@nimbit/config": "workspace:*", "@nimbit/types": "workspace:*", "@nimbit/ui": "workspace:*", "@nimbit/utils": "workspace:*", "axios": "^1.5.0", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-intersection-observer": "^9.16.0", "recharts": "^3.1.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}