"use strict";

var _class_apply_descriptor_update = require("./_class_apply_descriptor_update.cjs");
var _class_extract_field_descriptor = require("./_class_extract_field_descriptor.cjs");

function _class_private_field_update(receiver, privateMap) {
    var descriptor = _class_extract_field_descriptor._(receiver, privateMap, "update");
    return _class_apply_descriptor_update._(receiver, descriptor);
}
exports._ = _class_private_field_update;
